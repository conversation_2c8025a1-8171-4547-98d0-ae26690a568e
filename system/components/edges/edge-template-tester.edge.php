@props([
    'template_source' => '',
    'compiled_output' => '',
    'pipeline_steps' => [],
    'enabled_steps' => [],
    'step_outputs' => []
])
@if (!$compiled_output) {
<div class="p-6 max-w-full">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Edge Template Tester</h1>
        <p class="text-gray-600">Test and debug Edge template compilation with pipeline step control</p>
    </div>

    <!-- Pipeline Controls -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 class="text-lg font-semibold mb-3">Pipeline Controls</h3>
        <form id="pipeline-form" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            @foreach($pipeline_steps as $step)
                <label class="flex items-center space-x-2">
                    <input
                            type="checkbox"
                            name="enabled_steps[]"
                            value="{{ $step }}"
                            {{ in_array($step, $enabled_steps) ? 'checked' : '' }}
                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            hx-post="{{ APP_ROOT }}/api/edge_template_compile"
                            hx-include="#template-form, #pipeline-form"
                            hx-target="#results-container"
                            hx-swap="innerHTML"
                    >
                    <span class="text-sm">{{ str_replace('_', ' ', ucfirst($step)) }}</span>
                </label>
            @endforeach
        </form>

        <div class="mt-3 flex space-x-2">
            <button
                    type="button"
                    class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                    onclick="document.querySelectorAll('#pipeline-form input[type=checkbox]').forEach(cb => cb.checked = true); document.querySelector('#pipeline-form input[type=checkbox]').dispatchEvent(new Event('change'));"
            >
                Enable All
            </button>
            <button
                    type="button"
                    class="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
                    onclick="document.querySelectorAll('#pipeline-form input[type=checkbox]').forEach(cb => cb.checked = false); document.querySelector('#pipeline-form input[type=checkbox]').dispatchEvent(new Event('change'));"
            >
                Disable All
            </button>
        </div>
    </div>

    <!-- Template Input -->
    <div class="mb-6">
        <form id="template-form">
            <label for="template_source" class="block text-sm font-medium text-gray-700 mb-2">
                Edge Template Source
            </label>
            <textarea
                    id="template_source"
                    name="template_source"
                    rows="15"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                    placeholder="Enter your Edge template code here..."
                    hx-post="{{ APP_ROOT }}/api/edge_template_compile"
                    hx-include="#template-form, #pipeline-form"
                    hx-target="#results-container"
                    hx-swap="innerHTML"
                    hx-trigger="input changed delay:500ms"
            >{{ $template_source }}</textarea>
        </form>
    </div>

    <!-- Results Container -->
    <div id="results-container">
@else
        @if($template_source)
            <!-- Compiled Output -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3">Final Compiled Output</h3>
                <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto">
                    <pre class="text-sm"><code>{{ $compiled_output }}</code></pre>
                </div>
            </div>

            <!-- Pipeline Step Outputs -->
            @if($step_outputs)
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-3">Pipeline Step Outputs</h3>
                    <div class="space-y-4">
                        @foreach($step_outputs as $step => $output)
                            <div class="border border-gray-200 rounded-lg">
                                <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                                    <h4 class="font-medium text-gray-900">{{ str_replace('_', ' ', ucfirst($step)) }}</h4>
                                </div>
                                <div class="p-4">
                                    <div class="bg-gray-900 text-green-400 p-3 rounded overflow-auto">
                                        <pre class="text-sm"><code>{{ $output }}</code></pre>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        @endif
@endif
    </div>
</div>
