<!doctype html>
<html class="h-full bg-white">
<head><title>Autobooks</title>
<link rel="stylesheet" href="https://rsms.me/inter/inter.css">
<!--<link rel="stylesheet" href="css/style.css">-->
<link rel="shortcut icon" href="{{ APP_ROOT }}/system/img/favicon.ico" />
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>

<!-- Jodit Editor -->
<link rel="stylesheet" href="https://unpkg.com/jodit@3/build/jodit.min.css"/>
<script src="https://unpkg.com/jodit@3/build/jodit.min.js"></script>

<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
<script src="https://unpkg.com/htmx.org@2.0.3"></script>
<script src="https://unpkg.com/htmx-ext-class-tools@2.0.1/class-tools.js"></script>
<script defer src="<?= APP_ROOT ?>/resources/components/js/notification-handler.js"></script>
<script defer src="<?= APP_ROOT ?>/resources/js/htmx-sse.js"></script>


    <script>
        // Navigation tree initialization function - simplified for sortable only
        function initNavTree() {
            return {
                currentRoute: localStorage.getItem('currentNavRoute') || window.location.pathname.replace('<?= APP_ROOT ?>', '').replace(/^\/+|\/+$/g, ''),
                init() {
                    // Set initial route based on current URL
                    if (!localStorage.getItem('currentNavRoute')) {
                        localStorage.setItem('currentNavRoute', this.currentRoute);
                    }
                },
                updateRoute(route) {
                    this.currentRoute = route;
                    localStorage.setItem('currentNavRoute', route);
                    // Dispatch event to sync other nav instances
                    window.dispatchEvent(new CustomEvent('nav-route-changed', { detail: { route: route } }));
                }
            };
        }

        // Global function to update route from HTMX
        window.updateNavRoute = function(route) {
            const navTree = document.querySelector('[x-data*="initNavTree"]');
            if (navTree && navTree.__x && navTree.__x.$data) {
                navTree.__x.$data.updateRoute(route);
            }
        };

        // Listen for route changes from other nav instances
        window.addEventListener('nav-route-changed', (event) => {
            // Update all nav tree instances
            document.querySelectorAll('[x-data*="initNavTree"]').forEach(el => {
                if (el._x_dataStack && el._x_dataStack[0]) {
                    el._x_dataStack[0].currentRoute = event.detail.route;
                }
            });
        });



           // Wait for HTMX to load before setting up sortable integration
        document.addEventListener('DOMContentLoaded', function() {
            // Universal Sortable Integration - Works with any .sortable element
            htmx.onLoad(function (content) {
                // Find all sortable containers
                var sortables = content.querySelectorAll(".sortable");

                for (var i = 0; i < sortables.length; i++) {
                    var sortable = sortables[i];

                    // Skip if already initialized
                    if (sortable.hasAttribute('data-sortable-initialized')) {
                        continue;
                    }

                    // Mark as initialized
                    sortable.setAttribute('data-sortable-initialized', 'true');

                    // Get configuration from data attributes
                    var config = {
                        animation: 150,
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        dragClass: 'sortable-drag',
                        filter: '.htmx-indicator',
                        onMove: function (evt) {
                            return evt.related.className.indexOf('htmx-indicator') === -1;
                        }
                    };

                    // Add group configuration if specified
                    if (sortable.dataset.sortableGroup) {
                        config.group = {
                            name: sortable.dataset.sortableGroup,
                            pull: true,
                            put: true
                        };
                    }

                    // Add handle if specified
                    if (sortable.dataset.sortableHandle) {
                        config.handle = sortable.dataset.sortableHandle;
                    }

                    // Add additional options from data attributes
                    if (sortable.dataset.sortableSwapThreshold) {
                        config.swapThreshold = parseFloat(sortable.dataset.sortableSwapThreshold);
                    }

                    if (sortable.dataset.sortableFallbackOnBody === 'true') {
                        config.fallbackOnBody = true;
                    }

                    // Set up the onEnd handler to trigger HTMX
                    config.onEnd = function (evt) {
                        // Trigger the 'sorted' event on the container
                        var event = new CustomEvent('sorted', {
                            detail: {
                                item: evt.item,
                                oldIndex: evt.oldIndex,
                                newIndex: evt.newIndex,
                                from: evt.from,
                                to: evt.to
                            }
                        });
                        evt.from.dispatchEvent(event);

                        // Disable sorting temporarily
                        this.option("disabled", true);
                    };

                    // Add visual feedback handlers
                    config.onStart = function (evt) {
                        document.body.classList.add('sorting-active');
                        evt.item.classList.add('dragging');
                    };

                    config.onEnd = (function(originalOnEnd) {
                        return function(evt) {
                            // Remove visual feedback
                            document.body.classList.remove('sorting-active');
                            evt.item.classList.remove('dragging');

                            // Call original onEnd
                            originalOnEnd.call(this, evt);
                        };
                    })(config.onEnd);

                    // Create the sortable instance
                    var sortableInstance = new Sortable(sortable, config);

                    // Re-enable sorting after HTMX swap
                    sortable.addEventListener("htmx:afterSwap", function () {
                        sortableInstance.option("disabled", false);
                    });
                }
            });

        }); // End DOMContentLoaded

    </script>



<style>
    .htmx-settling-in {
        background-color: yellow;
        transition: background-color 1s ease-in-out;
    }
    .htmx-settling-out {
        background-color: white;
        transition: background-color 1s ease-in-out;
    }

    /* Sortable styles */
    .sortable-ghost {
        opacity: 0.4;
        background: rgba(59, 130, 246, 0.1) !important;
    }

    .sortable-chosen {
        background: rgba(59, 130, 246, 0.2) !important;
    }

    .sortable-drag {
        transform: rotate(5deg);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .drop-target {
        background: rgba(34, 197, 94, 0.2) !important;
        border: 2px dashed #22c55e !important;
    }

    .sorting-active .drag-handle {
        cursor: grabbing !important;
    }

    .drag-handle:hover {
        opacity: 0.8;
        transform: scale(1.1);
        transition: all 0.2s ease;
    }

    .dragging {
        opacity: 0.6;
        transform: rotate(2deg);
    }

    /* Ensure proper z-index for dragged items */
    .sortable-drag {
        z-index: 9999 !important;
    }

    /* Style for nested lists to maintain depth appearance */
    [data-depth="1"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.3);
    }

    [data-depth="2"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.5);
    }

    [data-depth="3"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.7);
    }
</style>
<style>
    html {
        font-family: InterVariable, sans-serif !important;
    }
</style>
    <!-- Include Jodit CSS and JS -->
    <link rel="stylesheet" href="https://unpkg.com/jodit@3/build/jodit.min.css"/>
    <script src="https://unpkg.com/jodit@3/build/jodit.min.js"></script>
</head>
