@props([
    'title' => 'api view',
    'description' => 'right hand page of the layout',
    'view' => '',
    'function_call' => '',
    'namespace' => 'api\\' . str_replace('/', '\\', SOURCE_APP_PATH . '\\' . SOURCE_PAGE . '\\'),
    'params' => INPUT_PARAMS,
])
@use system\modal_tabs;

@php
$endpoint = $view;


    $endpoint_exists = !empty($endpoint) && file_exists($endpoint);
    $api_error = false;
    $error_message = '';
@endphp
@print_rr([
    'namespace' => $namespace,
    'endpoint'=> $endpoint,
    'endpoint_exists' => $endpoint_exists ? 'true' : 'false' ,
    'function_call' => $function_call,
    'path_parts' => $path_parts
],'calling');


@if($endpoint_exists)
    @include $endpoint
@else
    @php
        if (!empty($endpoint)) {
            $error_message = "API file not found: {$endpoint}";
            $api_error = true;
            tcs_log($error_message, 'router_errors');
        }
    @endphp
@endif

@if($function_call)
  @php
          $path_parts = PATH_PARTS;
          if (count($path_parts)>2) {
              // Remove 'api' and 'system' from the start of path parts if they exist
              if ($path_parts[0] == 'api') {
                  array_shift($path_parts);
              }
              $sys = $usr = false;

              array_pop($path_parts); // Remove the function call from the path
              $namespace = 'api\\' . str_replace('/', '\\',  implode('\\', $path_parts) . '\\');
          }
          print_rr([
              'namespace' => $namespace,
              'endpoint'=> $endpoint,
              'endpoint_exists' => $endpoint_exists ? 'true' : 'false' ,
              'function_call' => $function_call,
              'path_parts' => $path_parts
          ],'calling');
          $function_call = str_replace('\\\\', '\\', str_replace('\\\\', '\\', $namespace.$function_call));
          print_rr($function_call,'calling in ' . $endpoint );

          if (function_exists($function_call)) {
             /* tcs_log(
                  print_rr(
                      [
                          'namespace' => $namespace,
                          'function_call' => $function_call,
                          'path_parts' => $path_parts
                      ],
                      'calling',
                      true,
                      true,
                      false,
                      true
                  ),
              ,'api_wins');
                */
              // Initialize modal tabs for API requests (before function call)
              $modal_tabs_file = FS_SYS_CLASSES . '/modal_tabs.class.php';
           //   if (file_exists($modal_tabs_file)) {
              //    include_once($modal_tabs_file);
                  $hx_target = $_SERVER['HTTP_HX_TARGET'] ?? '';
                  print_rr(['modal_tabs_file' => $modal_tabs_file,'file_exists' => file_exists($modal_tabs_file),'class_exists' => class_exists('modal_tabs', false), 'hx_target' => $hx_target],'modal_tabs_init');
                  if ($hx_target === 'modal_body') {
                      try {
                          modal_tabs::init();
                      } catch (Exception $e) {
                         print_rr($e,'modal_tabs_init_error');
                      }
                  }
            //  }

              echo $function_call(array_merge($_GET, $_POST));
          } else {
              $error_message = "API function not found: {$function_call}";
              $api_error = true;
              tcs_log($error_message,'api_router_errors');
              // Return JSON error for API calls
              header('Content-Type: application/json');
              echo json_encode(['error' => $error_message]);

              // Also trigger HTMX notification
              header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . addslashes($error_message) . '"}}');
          }

  @endphp
@endif

@if($api_error && !$function_call)
    @php
        // Return JSON error for API calls without function
        header('Content-Type: application/json');
        echo json_encode(['error' => $error_message]);

        // Also trigger HTMX notification
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . addslashes($error_message) . '"}}');
    @endphp
@endif
