<?php
namespace api\system\edge_template_tester;
use edge\Edge;

function edge_template_compile($data = []): string {
    // Get the template source from the request
    $template_source = $data['template_source'] ?? '';
    $enabled_steps = $data['enabled_steps'] ?? [];
    
    // Get the full pipeline from Edge class
    $full_pipeline = Edge::get_pipeline();
    
    // If no steps are enabled, enable all by default
    if (empty($enabled_steps)) {
        $enabled_steps = $full_pipeline;
    }
    
    $results = [
        'template_source' => $template_source,
        'compiled_output' => '',
        'pipeline_steps' => $full_pipeline,
        'enabled_steps' => $enabled_steps,
        'step_outputs' => []
    ];
    
    if (!empty($template_source)) {
        try {
            // Create a custom pipeline with only enabled steps
            $custom_pipeline = array_intersect($full_pipeline, $enabled_steps);

            // Track output after each step for debugging
            $step_outputs = [];
            $current_source = $template_source;

            // Use the Edge compile method with custom pipeline
            $results['compiled_output'] = htmlspecialchars(Edge::compile($current_source, 'template_tester', [], $custom_pipeline));

            // For step-by-step debugging, process each step individually

            foreach ($custom_pipeline as $step) {
                try {
                    // Process just this one step
                    $single_step_result = Edge::compile($current_source, 'template_tester', [], [$step]);
                    $current_source = $single_step_result;
                    $step_outputs[$step] = htmlspecialchars($single_step_result);
                } catch (Exception $step_error) {
                    $step_outputs[$step] = "Error in step '$step': " . $step_error->getMessage();
                }
            }

            $results['step_outputs'] = $step_outputs;

        } catch (Exception $e) {
            $results['compiled_output'] = "Error: " . $e->getMessage() . "\n\nStack trace:\n" . $e->getTraceAsString();
            $results['step_outputs'] = [];
        }
    }
    
    // Render the results back to the template
    return Edge::render('edge-template-tester', $results);
}

// Handle the default case when no specific function is called
if (!isset($action) || !function_exists($action)) {
    return edge_template_compile($_POST);
}
?>
