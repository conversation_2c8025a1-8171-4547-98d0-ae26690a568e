<?php
use edge\Edge;
use system\users;

// Ensure only admin/dev can access this page
users::requireRole('admin');

// Get the full pipeline from Edge class
$full_pipeline = Edge::get_pipeline();

// Initialize with all steps enabled
$initial_data = [
    'template_source' => '',
    'compiled_output' => '',
    'pipeline_steps' => $full_pipeline,
    'enabled_steps' => $full_pipeline,
    'step_outputs' => []
];

// Render the Edge template tester
echo Edge::render('edge-template-tester', $initial_data);
?>
