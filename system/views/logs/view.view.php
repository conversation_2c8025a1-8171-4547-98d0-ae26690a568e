<?php
namespace system\logs;
use system\users;
use edge\Edge;
use function system\logs\process_log_content;
use function system\logs\formatFileSize;

// Ensure only admin/dev can access this page
users::requireRole('admin');
$log_data = process_log_content($_GET);
?>

<div class="p-10 sm:px-6 lg:px-8 max-w-full overflow-hidden">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-base font-semibold leading-6 text-gray-900">Log Viewer: <?= htmlspecialchars($log_data['logFile']); ?></h1>
            <p class="mt-2 text-sm text-gray-700">
                Size: <?= formatFileSize(filesize($log_data['filePath'])); ?> |
                Last Modified: <?= date('Y-m-d H:i:s', filemtime($log_data['filePath'])); ?>
            </p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <a href="#" hx-get="<?= APP_ROOT . DS .APP_PATH ?>/logs" hx-target="#content_wrapper" data-apppath="<?= APP_PATH ?>" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                Back to List
            </a>
            <a href="#" hx-get="<?= APP_ROOT . DS . APP_PATH ?>/logs?clear=<?= urlencode($log_data['logFile']); ?>" class="ml-3 rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600" onclick="return confirm('Are you sure you want to clear this log file?');">
                Clear Log
            </a>
        </div>
    </div>

    <div class="mt-8 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div class="flex-1">
            <input type="hidden" id="log_file" name="file" value="<?= htmlspecialchars($log_data['logFile']); ?>">
            <input type="hidden" id="refresh_rate" name="refresh" value="<?= $log_data['autoRefresh']; ?>">

            <!-- This will be used by the data table's built-in search functionality -->
            <div class="flex rounded-md shadow-sm">
                <input type="text"
                       id="log_search"
                       name="search_terms"
                       value="<?= htmlspecialchars($log_data['search']); ?>"
                       placeholder="Search in log..."
                       class="block w-full rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                       hx-include="#log_file, #refresh_rate"
                       hx-trigger="keyup changed delay:500ms"
                       hx-get="<?= APP_ROOT . APP_PATH ?>/view"
                       hx-target="#log_table_container"
                       hx-swap="innerHTML">
            </div>
        </div>

        <div class="flex items-center">
            <div class="flex items-center">
                <label for="refresh" class="mr-2 text-sm font-medium text-gray-700">Auto-refresh:</label>
                <select name="refresh" id="refresh"
                        onchange="document.getElementById('refresh_rate').value = this.value; refreshLogTable();"
                        class="rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                    <option value="0" <?= $log_data['autoRefresh'] == 0 ? 'selected' : ''; ?>>Off</option>
                    <option value="5" <?= $log_data['autoRefresh'] == 5 ? 'selected' : ''; ?>>5 seconds</option>
                    <option value="10" <?= $log_data['autoRefresh'] == 10 ? 'selected' : ''; ?>>10 seconds</option>
                    <option value="30" <?= $log_data['autoRefresh'] == 30 ? 'selected' : ''; ?>>30 seconds</option>
                    <option value="60" <?= $log_data['autoRefresh'] == 60 ? 'selected' : ''; ?>>1 minute</option>
                </select>
            </div>

            <?php if ($log_data['autoRefresh'] > 0): ?>
            <script>
                // Set up auto-refresh functionality
                let refreshInterval;

                function refreshLogTable() {
                    const refreshRate = document.getElementById('refresh_rate').value;
                    const logFile = document.getElementById('log_file').value;
                    const searchTerms = document.getElementById('log_search')?.value || '';

                    // Clear any existing interval
                    clearInterval(refreshInterval);

                    if (refreshRate > 0) {
                        // Set up new interval
                        refreshInterval = setInterval(() => {
                            // Use HTMX to refresh the table
                            htmx.ajax('GET', '<?= APP_ROOT . APP_PATH ?>/view', {
                                target: '#log_table_container',
                                swap: 'innerHTML',
                                values: {
                                    file: logFile,
                                    refresh: refreshRate,
                                    search_terms: searchTerms
                                }
                            });
                        }, refreshRate * 1000);
                    }
                }

                // Initialize auto-refresh
                document.addEventListener('DOMContentLoaded', refreshLogTable);
            </script>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!empty($log_data['search'])): ?>
        <div class="mt-4 rounded-md bg-blue-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-blue-800">Search results for: <strong><?= htmlspecialchars($log_data['search']); ?></strong> (<?= $log_data['total']; ?> matches)</p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($log_data['total'] > 0): ?>
        <div class="mt-4 flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing <span class="font-medium"><?= count($log_data['entries']); ?></span> of <span class="font-medium"><?= $log_data['total']; ?></span> entries
            </div>

            <div class="flex items-center space-x-2">
                <div class="flex items-center">
                    <label for="per_page" class="mr-2 text-sm font-medium text-gray-700">Entries per page:</label>
                    <select name="per_page" id="per_page"
                            onchange="updatePerPage(this.value)"
                            class="rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                        <option value="50" <?= $log_data['perPage'] == 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?= $log_data['perPage'] == 100 ? 'selected' : ''; ?>>100</option>
                        <option value="250" <?= $log_data['perPage'] == 250 ? 'selected' : ''; ?>>250</option>
                        <option value="500" <?= $log_data['perPage'] == 500 ? 'selected' : ''; ?>>500</option>
                        <option value="1000" <?= $log_data['perPage'] == 1000 ? 'selected' : ''; ?>>1000</option>
                    </select>
                </div>
            </div>
        </div>

        <script>
            function updatePerPage(perPage) {
                const logFile = document.getElementById('log_file').value;
                const refreshRate = document.getElementById('refresh_rate').value;
                const searchTerms = document.getElementById('log_search')?.value || '';

                // Use HTMX to refresh the table with new per_page value
                htmx.ajax('GET', '<?= APP_ROOT . APP_PATH ?>/view', {
                    target: '#log_table_container',
                    swap: 'innerHTML',
                    values: {
                        file: logFile,
                        refresh: refreshRate,
                        search_terms: searchTerms,
                        per_page: perPage,
                        page: 1 // Reset to first page when changing items per page
                    }
                });
            }
        </script>
    <?php endif; ?>

    <div id="log_table_container" class="mt-6 flow-root">
        <?php render_log_table_entries($log_data); ?>
    </div>

    <!-- Pagination is now handled by the data-table component -->
    <?php /*else: ?>
        <div class="mt-6 rounded-md bg-gray-50 p-8 text-center">
            <p class="text-sm text-gray-500">No log entries found<?= !empty($logdata['search']) ? ' matching your search criteria' : ''; ?>.</p>
        </div>
    <?php endif;*/ ?>
</div>
