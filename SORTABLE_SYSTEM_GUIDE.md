# Universal Sortable System Guide

This guide explains how to use the new simplified sortable system that works with any element having the `.sortable` class and uses HTMX inline attributes for server communication.

## Overview

The new sortable system is designed to be:
- **Simple**: One JavaScript function handles all sortable containers
- **Reusable**: Works with any HTML structure using the `.sortable` class
- **HTMX-first**: Uses HTMX inline attributes instead of complex JavaScript
- **Form-free**: Uses standard HTTP submissions without requiring form wrappers

## Basic Usage

### 1. HTML Structure

```html
<div class="sortable"
     hx-post="/api/your-endpoint"
     hx-trigger="sorted"
     hx-target="#result"
     hx-swap="innerHTML">
    
    <div class="sortable-item" data-item-id="1">
        <input type="hidden" name="item_ids[]" value="1">
        Item 1 Content
    </div>
    
    <div class="sortable-item" data-item-id="2">
        <input type="hidden" name="item_ids[]" value="2">
        Item 2 Content
    </div>
</div>
```

### 2. Required Elements

- **Container**: Must have `sortable` class
- **Items**: Should have unique identifiers (e.g., `data-item-id`)
- **Hidden Inputs**: For form submission (e.g., `name="item_ids[]"`)
- **HTMX Attributes**: For server communication

## Configuration Options

### Data Attributes for Sortable Container

| Attribute | Description | Example |
|-----------|-------------|---------|
| `data-sortable-group` | Allow items to move between containers | `data-sortable-group="tasks"` |
| `data-sortable-handle` | Specify drag handle selector | `data-sortable-handle=".drag-handle"` |
| `data-sortable-swap-threshold` | Set swap threshold (0-1) | `data-sortable-swap-threshold="0.65"` |
| `data-sortable-fallback-on-body` | Enable fallback on body | `data-sortable-fallback-on-body="true"` |

### HTMX Attributes for Server Communication

| Attribute | Description | Example |
|-----------|-------------|---------|
| `hx-post` | Server endpoint | `hx-post="/api/reorder"` |
| `hx-trigger` | Trigger event | `hx-trigger="sorted"` |
| `hx-target` | Response target | `hx-target="#result"` |
| `hx-swap` | Swap method | `hx-swap="innerHTML"` |

## Examples

### Basic List Sorting

```html
<div class="sortable"
     hx-post="/api/sortable/reorder_items"
     hx-trigger="sorted"
     hx-target="#status"
     hx-swap="innerHTML">
    
    <div data-item-id="item1">
        <input type="hidden" name="item_ids[]" value="item1">
        First Item
    </div>
    <div data-item-id="item2">
        <input type="hidden" name="item_ids[]" value="item2">
        Second Item
    </div>
</div>
```

### Sortable with Drag Handle

```html
<div class="sortable"
     data-sortable-handle=".drag-handle"
     hx-post="/api/sortable/reorder_tasks"
     hx-trigger="sorted"
     hx-target="#task-status">
    
    <div data-task-id="task1">
        <input type="hidden" name="task_ids[]" value="task1">
        <span class="drag-handle">⋮⋮⋮</span>
        <span>Task Content</span>
    </div>
</div>
```

### Sortable Groups (Move Between Lists)

```html
<!-- List 1 -->
<div class="sortable"
     data-sortable-group="shared-group"
     hx-post="/api/sortable/move_between_lists"
     hx-trigger="sorted">
    <input type="hidden" name="list_id" value="todo">
    <!-- items here -->
</div>

<!-- List 2 -->
<div class="sortable"
     data-sortable-group="shared-group"
     hx-post="/api/sortable/move_between_lists"
     hx-trigger="sorted">
    <input type="hidden" name="list_id" value="done">
    <!-- items here -->
</div>
```

## Server-Side Implementation

### Basic API Endpoint

```php
function reorder_items($p) {
    $item_ids = $p['item_ids'] ?? [];
    
    if (empty($item_ids)) {
        return '<div class="text-red-600">No items to reorder</div>';
    }
    
    // Update database
    foreach ($item_ids as $index => $item_id) {
        $db->update('your_table', [
            'sort_order' => $index + 1
        ], ['id' => $item_id]);
    }
    
    return '<div class="text-green-600">✓ Items reordered successfully!</div>';
}
```

### Generic Reorder Handler

```php
function generic_reorder($p) {
    $table_name = $p['table_name'] ?? '';
    $item_ids = $p['item_ids'] ?? [];
    
    if (empty($table_name) || empty($item_ids)) {
        return '<div class="text-red-600">Missing required parameters</div>';
    }
    
    $db = new database();
    foreach ($item_ids as $index => $item_id) {
        $db->update($table_name, [
            'sort_order' => $index + 1,
            'updated_at' => date('Y-m-d H:i:s')
        ], ['id' => $item_id]);
    }
    
    return '<div class="text-green-600">✓ Reordered successfully!</div>';
}
```

## Migration from Old System

### Before (Complex JavaScript)

```javascript
// Old system required specific JavaScript for each sortable type
var navSortables = content.querySelectorAll(".nav-sortable");
var columnSortables = content.querySelectorAll(".column-sortable");
var fieldSortables = content.querySelectorAll(".field-container");
// ... lots of repetitive code
```

### After (Universal System)

```javascript
// New system handles all sortables with one function
var sortables = content.querySelectorAll(".sortable");
// ... single reusable implementation
```

### Template Changes

**Before:**
```html
<form class="nav-sortable" hx-post="/api/endpoint" hx-trigger="end">
    <!-- items -->
</form>
```

**After:**
```html
<div class="sortable" hx-post="/api/endpoint" hx-trigger="sorted">
    <!-- items -->
</div>
```

## Best Practices

1. **Always include hidden inputs** for form data submission
2. **Use meaningful data attributes** for item identification
3. **Provide user feedback** through HTMX target updates
4. **Handle errors gracefully** in your API endpoints
5. **Test with different item counts** and edge cases
6. **Use drag handles** for complex layouts to avoid accidental sorting

## Troubleshooting

### Common Issues

1. **Items not sorting**: Check if container has `sortable` class
2. **No server request**: Verify HTMX attributes are correct
3. **Wrong data sent**: Ensure hidden inputs have proper names
4. **Handle not working**: Check `data-sortable-handle` selector

### Debug Tips

1. Check browser console for HTMX events
2. Verify form data in Network tab
3. Test API endpoints independently
4. Use `tcs_log()` for server-side debugging

## Files Modified

- `system/components/layouts/layout-head.edge.php` - Universal sortable JavaScript
- `system/components/edges/nav-tree.edge.php` - Updated navigation tree
- `system/api/sortable.api.php` - Example API endpoints
- `system/components/edges/sortable-example.edge.php` - Usage examples

The new system is backward compatible and existing sortable implementations will continue to work while you migrate them to the new simplified approach.
